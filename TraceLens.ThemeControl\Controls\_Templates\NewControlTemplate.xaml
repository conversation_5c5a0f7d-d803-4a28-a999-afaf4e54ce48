﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:controls="clr-namespace:TraceLens.ThemeControl.Controls.YourControlName">

    <!-- YourControlName 控件样式 -->
    <Style TargetType="{x:Type controls:YourControlName}">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type controls:YourControlName}">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="4">
                        <ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                          VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                          Margin="{TemplateBinding Padding}"/>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        
        <!-- 默认属性设置 -->
        <Setter Property="Background" Value="{StaticResource FillExtraLightBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderBaseBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="8"/>
        <Setter Property="HorizontalContentAlignment" Value="Center"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
    </Style>

    <!-- YourControlName 预定义样式 -->
    
    <!-- 主色调样式 -->
    <Style x:Key="YourControlNamePrimary" TargetType="{x:Type controls:YourControlName}" 
           BasedOn="{StaticResource {x:Type controls:YourControlName}}">
        <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="Foreground" Value="White"/>
    </Style>
    
    <!-- 成功样式 -->
    <Style x:Key="YourControlNameSuccess" TargetType="{x:Type controls:YourControlName}" 
           BasedOn="{StaticResource {x:Type controls:YourControlName}}">
        <Setter Property="Background" Value="{StaticResource SuccessBrush}"/>
        <Setter Property="Foreground" Value="White"/>
    </Style>
    
    <!-- 警告样式 -->
    <Style x:Key="YourControlNameWarning" TargetType="{x:Type controls:YourControlName}" 
           BasedOn="{StaticResource {x:Type controls:YourControlName}}">
        <Setter Property="Background" Value="{StaticResource WarningBrush}"/>
        <Setter Property="Foreground" Value="White"/>
    </Style>
    
    <!-- 危险样式 -->
    <Style x:Key="YourControlNameDanger" TargetType="{x:Type controls:YourControlName}" 
           BasedOn="{StaticResource {x:Type controls:YourControlName}}">
        <Setter Property="Background" Value="{StaticResource DangerBrush}"/>
        <Setter Property="Foreground" Value="White"/>
    </Style>

</ResourceDictionary>
