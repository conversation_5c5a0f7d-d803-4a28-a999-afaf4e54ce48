﻿using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace TraceLens.ThemeControl.Controls.YourControlName
{
    /// <summary>
    /// YourControlName - [控件描述]
    /// [详细功能说明]
    /// </summary>
    public class YourControlName : ContentControl
    {
        #region 依赖属性

        /// <summary>
        /// 示例属性
        /// </summary>
        public static readonly DependencyProperty SamplePropertyProperty =
            DependencyProperty.Register(nameof(SampleProperty), typeof(string), typeof(YourControlName),
                new PropertyMetadata(string.Empty, OnSamplePropertyChanged));

        #endregion

        #region 属性

        /// <summary>
        /// 示例属性
        /// </summary>
        public string SampleProperty
        {
            get => (string)GetValue(SamplePropertyProperty);
            set => SetValue(SamplePropertyProperty, value);
        }

        #endregion

        #region 构造函数

        static YourControlName()
        {
            DefaultStyleKeyProperty.OverrideMetadata(typeof(YourControlName), 
                new FrameworkPropertyMetadata(typeof(YourControlName)));
        }

        public YourControlName()
        {
            // 初始化代码
        }

        #endregion

        #region 重写方法

        public override void OnApplyTemplate()
        {
            base.OnApplyTemplate();
            // 模板应用后的初始化代码
        }

        #endregion

        #region 私有方法

        private static void OnSamplePropertyChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is YourControlName control)
            {
                control.OnSamplePropertyChanged((string)e.OldValue, (string)e.NewValue);
            }
        }

        private void OnSamplePropertyChanged(string oldValue, string newValue)
        {
            // 属性变更处理逻辑
        }

        #endregion
    }
}
