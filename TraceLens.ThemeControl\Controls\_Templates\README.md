﻿# 控件模板

这个文件夹包含了创建新控件时使用的模板文件。

## 📁 模板文件

- `NewControlTemplate.cs` - 控件类模板
- `NewControlTemplate.xaml` - 控件样式模板

## 🚀 使用模板创建新控件

### 步骤 1：复制模板文件
1. 在 `Controls/` 目录下创建新的控件文件夹，如 `MyNewControl/`
2. 将模板文件复制到新文件夹中
3. 重命名文件：
   - `NewControlTemplate.cs` → `MyNewControl.cs`
   - `NewControlTemplate.xaml` → `MyNewControl.xaml`

### 步骤 2：替换占位符
在复制的文件中，将以下占位符替换为实际值：

**在 .cs 文件中：**
- `YourControlName` → 实际控件名称（如 `MyNewControl`）
- `[控件描述]` → 控件的简短描述
- `[详细功能说明]` → 控件的详细功能说明
- `SampleProperty` → 实际的属性名称

**在 .xaml 文件中：**
- `YourControlName` → 实际控件名称
- `clr-namespace:TraceLens.ThemeControl.Controls.YourControlName` → 实际命名空间

### 步骤 3：实现控件逻辑
根据控件的具体需求：
1. 添加必要的依赖属性
2. 实现控件的核心逻辑
3. 设计控件的外观模板
4. 创建预定义样式

### 步骤 4：更新主题引用
在 `Themes/Industrial/Theme.xaml` 中添加新控件的样式引用：
```xml
<ResourceDictionary Source="pack://application:,,,/TraceLens.ThemeControl;component/Controls/MyNewControl/MyNewControl.xaml"/>
```

### 步骤 5：更新控件索引
在 `Controls/ControlsIndex.cs` 中添加新控件的信息。

## 📋 模板说明

### 控件类模板特性
- 继承自 `ContentControl`，适合大多数控件场景
- 包含标准的依赖属性模式
- 提供属性变更回调机制
- 遵循 WPF 控件开发最佳实践

### 样式模板特性
- 使用 Element UI 颜色体系
- 提供基础样式和多种预定义样式
- 支持样式继承和覆盖
- 包含常用的视觉状态

## 🎯 最佳实践

1. **命名规范**
   - 控件名使用 PascalCase
   - 属性名使用 PascalCase
   - 样式名使用 `ControlName + 修饰符` 格式

2. **依赖属性**
   - 为所有可绑定的属性创建依赖属性
   - 提供合适的默认值
   - 实现属性变更回调

3. **样式设计**
   - 使用 Element UI 颜色体系
   - 提供多种预定义样式
   - 确保样式的一致性

4. **文档**
   - 为所有公共成员添加 XML 注释
   - 创建使用示例
   - 更新控件索引

---

**注意：** 这些模板提供了一个良好的起点，但根据具体控件的需求，可能需要进行相应的调整和扩展。
