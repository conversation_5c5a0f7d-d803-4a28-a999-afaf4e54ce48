﻿<UserControl x:Class="TraceLens.ThemeControl.Controls.HIcon.HIconUsageExample"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:controls="clr-namespace:TraceLens.ThemeControl.Controls.HIcon">
    
    <ScrollViewer>
        <StackPanel Margin="20">
            <TextBlock Text="HIcon 控件使用示例" 
                       Style="{StaticResource MainTitleText}" 
                       Margin="0,0,0,20"/>
            
            <!-- 基础用法 -->
            <TextBlock Text="基础用法" Style="{StaticResource TitleText}" Margin="0,0,0,10"/>
            <StackPanel Orientation="Horizontal" Margin="0,0,0,20">
                <controls:HIcon IconName="home" Margin="5"/>
                <controls:HIcon IconName="user" Margin="5"/>
                <controls:HIcon IconName="search" Margin="5"/>
                <controls:HIcon IconName="cog" Margin="5"/>
                <controls:HIcon IconName="heart" Margin="5"/>
            </StackPanel>
            
            <!-- 不同大小 -->
            <TextBlock Text="不同大小" Style="{StaticResource TitleText}" Margin="0,0,0,10"/>
            <StackPanel Orientation="Horizontal" Margin="0,0,0,20">
                <controls:HIcon IconName="star" Style="{StaticResource HIconSmall}" Margin="5"/>
                <controls:HIcon IconName="star" Style="{StaticResource HIconMedium}" Margin="5"/>
                <controls:HIcon IconName="star" Style="{StaticResource HIconLarge}" Margin="5"/>
                <controls:HIcon IconName="star" Style="{StaticResource HIconExtraLarge}" Margin="5"/>
            </StackPanel>
            
            <!-- 不同颜色 -->
            <TextBlock Text="不同颜色" Style="{StaticResource TitleText}" Margin="0,0,0,10"/>
            <StackPanel Orientation="Horizontal" Margin="0,0,0,20">
                <controls:HIcon IconName="check-circle" Style="{StaticResource HIconSuccess}" Margin="5"/>
                <controls:HIcon IconName="exclamation-triangle" Style="{StaticResource HIconWarning}" Margin="5"/>
                <controls:HIcon IconName="times-circle" Style="{StaticResource HIconDanger}" Margin="5"/>
                <controls:HIcon IconName="info-circle" Style="{StaticResource HIconInfo}" Margin="5"/>
                <controls:HIcon IconName="cog" Style="{StaticResource HIconPrimary}" Margin="5"/>
            </StackPanel>
            
            <!-- 常用图标展示 -->
            <TextBlock Text="常用图标" Style="{StaticResource TitleText}" Margin="0,0,0,10"/>
            <WrapPanel Margin="0,0,0,20">
                <!-- 导航图标 -->
                <StackPanel Orientation="Horizontal" Margin="10">
                    <controls:HIcon IconName="arrow-left" Margin="2"/>
                    <controls:HIcon IconName="arrow-right" Margin="2"/>
                    <controls:HIcon IconName="arrow-up" Margin="2"/>
                    <controls:HIcon IconName="arrow-down" Margin="2"/>
                </StackPanel>
                
                <!-- 文件操作 -->
                <StackPanel Orientation="Horizontal" Margin="10">
                    <controls:HIcon IconName="file" Margin="2"/>
                    <controls:HIcon IconName="folder" Margin="2"/>
                    <controls:HIcon IconName="save" Margin="2"/>
                    <controls:HIcon IconName="download" Margin="2"/>
                    <controls:HIcon IconName="upload" Margin="2"/>
                </StackPanel>
                
                <!-- 编辑操作 -->
                <StackPanel Orientation="Horizontal" Margin="10">
                    <controls:HIcon IconName="edit" Margin="2"/>
                    <controls:HIcon IconName="trash" Margin="2"/>
                    <controls:HIcon IconName="copy" Margin="2"/>
                    <controls:HIcon IconName="cut" Margin="2"/>
                    <controls:HIcon IconName="paste" Margin="2"/>
                </StackPanel>
                
                <!-- 媒体控制 -->
                <StackPanel Orientation="Horizontal" Margin="10">
                    <controls:HIcon IconName="play" Margin="2"/>
                    <controls:HIcon IconName="pause" Margin="2"/>
                    <controls:HIcon IconName="stop" Margin="2"/>
                    <controls:HIcon IconName="forward" Margin="2"/>
                    <controls:HIcon IconName="backward" Margin="2"/>
                </StackPanel>
                
                <!-- 通信图标 -->
                <StackPanel Orientation="Horizontal" Margin="10">
                    <controls:HIcon IconName="envelope" Margin="2"/>
                    <controls:HIcon IconName="phone" Margin="2"/>
                    <controls:HIcon IconName="comment" Margin="2"/>
                    <controls:HIcon IconName="comments" Margin="2"/>
                </StackPanel>
            </WrapPanel>
            
            <!-- 使用说明 -->
            <TextBlock Text="使用说明" Style="{StaticResource TitleText}" Margin="0,20,0,10"/>
            <TextBlock Style="{StaticResource BodyText}" TextWrapping="Wrap" Margin="0,0,0,10">
                <Run Text="1. 直接使用图标名称（不需要 fa- 前缀）："/>
                <LineBreak/>
                <Run Text="   &lt;controls:HIcon IconName=&quot;home&quot; /&gt;" FontFamily="{StaticResource MonospaceFont}"/>
                <LineBreak/><LineBreak/>
                <Run Text="2. 设置大小和颜色："/>
                <LineBreak/>
                <Run Text="   &lt;controls:HIcon IconName=&quot;star&quot; IconSize=&quot;24&quot; IconColor=&quot;Red&quot; /&gt;" FontFamily="{StaticResource MonospaceFont}"/>
                <LineBreak/><LineBreak/>
                <Run Text="3. 使用预定义样式："/>
                <LineBreak/>
                <Run Text="   &lt;controls:HIcon IconName=&quot;check&quot; Style=&quot;{StaticResource HIconSuccess}&quot; /&gt;" FontFamily="{StaticResource MonospaceFont}"/>
            </TextBlock>
            
            <!-- 支持的图标列表 -->
            <TextBlock Text="支持的图标（部分）" Style="{StaticResource TitleText}" Margin="0,20,0,10"/>
            <TextBlock Style="{StaticResource BodySmallText}" TextWrapping="Wrap">
                <Run Text="home, user, users, cog, cogs, search, plus, minus, times, check, arrow-left, arrow-right, arrow-up, arrow-down, "/>
                <Run Text="file, folder, save, download, upload, edit, trash, copy, cut, paste, play, pause, stop, "/>
                <Run Text="envelope, phone, comment, heart, star, info, warning, check-circle, times-circle, "/>
                <Run Text="calendar, clock-o, refresh, print, share, link... 等 600+ 图标"/>
            </TextBlock>
        </StackPanel>
    </ScrollViewer>
</UserControl>
